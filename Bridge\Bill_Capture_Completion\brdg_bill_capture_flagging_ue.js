/**
 * @description Flags vendor bills created via Bill Capture that meet specific business criteria
 *
 * Flagging occurs when BOTH conditions are met:
 * 1. Bill was created via Bill Capture (verified through systemnote)
 * 2. Bill meets all business criteria:
 *    - Under Bridge (BRDG) subsidiary
 *    - Standalone bill (not linked to Purchase Order)
 *    - Contains at least one inventory part item
 *
 * @NApiVersion 2.1
 * @NScriptType UserEventScript
 *
 * <AUTHOR>
 * @module brdg_bill_capture_flagging_ue
 */

define([
    "require",
    "N/record",
    "N/query",
    "N/runtime",
    "../../Classes/vlmd_custom_error_object",
    "../Libraries/brdg_helper_functions_lib",
], (require) => {
    const record = require("N/record");
    const query = require("N/query");
    const runtime = require("N/runtime");
    const CustomErrorObject = require("../../Classes/vlmd_custom_error_object");
    const bridgeHelperFunctionsLib = require("../Libraries/brdg_helper_functions_lib");
    const customErrorObject = new CustomErrorObject();

    const beforeLoad = (context) => {
        try {
            if (context.type !== context.UserEventType.VIEW) return;

            // Get current user's role
            const currentUserRole = runtime.getCurrentUser().role;
            const isFromBillCapture = context.newRecord.getValue({
                fieldId: "custbody_brdg_bill_capture",
            });

            // Early return if user is not admin or system, or bill is not from Bill Capture
            if(currentUserRole === 3 || currentUserRole === 1197 || !isFromBillCapture) return;

            context.form.removeButton({ id: "edit" });
        } catch (err) {
            customErrorObject.throwError({
                summaryText: `ERROR_DISABLING_BUTTONS`,
                error: err,
            });
        }
    };

    const afterSubmit = (context) => {
        try {
            if(context.type != context.UserEventType.CREATE && context.type != context.UserEventType.EDIT) return;

            let vendorBill = context.newRecord;
            let vendorBillId = vendorBill.id;

            // Check if this bill meets all flagging criteria
            if (shouldFlagBillForCapture(vendorBillId)) {
                log.audit('Vendor Bill Flagged for Bill Capture', `Vendor Bill ID ${vendorBillId} meets all criteria for bill capture flagging.`);
                record.submitFields({
                    type: record.Type.VENDOR_BILL,
                    id: vendorBillId,
                    values: {
                        custbody_brdg_bill_capture: true,
                    },
                });
            }
        } catch (err) {
            customErrorObject.throwError({
                summaryText: `ERROR_FLAGGING_BILL`,
                error: err,
            });
        }
    };

    /**
     * Determines if a vendor bill should be flagged for bill capture completion
     * based on BOTH conditions:
     * 1. Bill was created via Bill Capture (systemnote check)
     * 2. Bill meets all three business criteria:
     *    - Bill is under a Bridge (BRDG) subsidiary
     *    - Bill is standalone (not linked to a Purchase Order)
     *    - Bill contains at least one inventory part item
     *
     * @param {string|number} vendorBillId - The ID of the vendor bill to check
     * @returns {boolean} True if bill was created via Bill Capture AND meets all criteria
     */
    const shouldFlagBillForCapture = (vendorBillId) => {
        try {
            // First, check if bill was created via Bill Capture
            const billCaptureQuery = /*sql*/ `
                SELECT DISTINCT
                    BUILTIN.DF(context) AS context
                FROM
                    systemnote
                WHERE
                    recordid = ? AND
                    BUILTIN.DF(context) = 'Bill Capture'
            `;

            const billCaptureResults = query.runSuiteQL({
                query: billCaptureQuery,
                params: [vendorBillId],
            }).asMappedResults();

            // If not created via Bill Capture, don't flag
            if (billCaptureResults.length === 0) {
                log.debug('Bill Not Created via Bill Capture', {
                    billId: vendorBillId,
                    reason: 'No Bill Capture context found in systemnote'
                });
                return false;
            }

            // If created via Bill Capture, check business criteria
            const bridgeSubsidiaries = bridgeHelperFunctionsLib.getBridgeSubsidiaries();
            const bridgeSubsidiariesSql = bridgeHelperFunctionsLib.getSuiteqlBridgeSubsidiaryList(bridgeSubsidiaries);

            // Query to check all three business criteria at once
            const flaggingCriteriaQuery = /*sql*/ `
                SELECT DISTINCT
                    Transaction.id,
                    Transaction.tranid,
                    BUILTIN.DF(Transaction.entity) AS vendor
                FROM
                    Transaction
                INNER JOIN TransactionLine ON
                    TransactionLine.transaction = Transaction.id
                LEFT OUTER JOIN PreviousTransactionLineLink AS PTLL
                    ON PTLL.nextdoc = Transaction.id
                WHERE
                    Transaction.id = ?
                    AND Transaction.type = 'VendBill'
                    AND TransactionLine.subsidiary IN ${bridgeSubsidiariesSql}  -- Bridge subsidiary check
                    AND PTLL.nextdoc IS NULL  -- Standalone bill check (not linked to PO)
                    AND EXISTS (  -- Inventory item check
                        SELECT 1
                        FROM TransactionLine
                        WHERE TransactionLine.transaction = Transaction.id
                        AND TransactionLine.itemtype = 'InvtPart'
                    )
            `;

            const criteriaResults = query.runSuiteQL({
                query: flaggingCriteriaQuery,
                params: [vendorBillId],
            }).asMappedResults();

            const meetsBusinessCriteria = criteriaResults.length > 0;

            if (meetsBusinessCriteria) {
                log.debug('Bill Capture Flagging: All Conditions Met', {
                    billId: vendorBillId,
                    billNumber: criteriaResults[0]?.tranid,
                    vendor: criteriaResults[0]?.vendor,
                    createdViaBillCapture: true,
                    meetsBusinessCriteria: true
                });
            } else {
                log.debug('Bill Capture Flagging: Business Criteria Not Met', {
                    billId: vendorBillId,
                    createdViaBillCapture: true,
                    meetsBusinessCriteria: false,
                    reason: 'One or more criteria failed: Bridge subsidiary, standalone bill, or inventory items'
                });
            }

            return meetsBusinessCriteria;

        } catch (error) {
            log.error('Error in shouldFlagBillForCapture', {
                billId: vendorBillId,
                error: error.message
            });
            return false;
        }
    };

    return {
        beforeLoad,
        afterSubmit,
    };

});

